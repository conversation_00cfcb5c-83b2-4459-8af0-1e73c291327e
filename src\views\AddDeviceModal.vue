<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <div class="support-page">
                <Teleport to="body">
                    <AddDevice @close="toSAM" />
                </Teleport>
            </div>
        </div>
    </div>
</template>

<script setup>
import AddDevice from '../components/AddDevice.vue'
import TopNav from '../components/TopNav.vue';
import { useRouter } from 'vue-router'

const router = useRouter()

const toSAM = () => {
    router.push('/support-app-management')
}


</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}
</style>