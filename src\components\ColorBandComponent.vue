<template>
  <div class="color-band-container">
    <!-- 顶部区域栏 -->
    <div class="header-section">
      <div class="realtime-label">实时</div>
      <div class="date-picker-wrapper">
        <input 
          type="date" 
          v-model="selectedDate" 
          class="date-picker"
          @change="handleDateChange"
        />
        <div class="date-display">{{ formatDate(selectedDate) }}</div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- 左侧蓝色方块 -->
      <div class="pm-value-block">
        <div class="pm-value">{{ pmValue }}</div>
        <div class="pm-label">PM₁₀</div>
      </div>

      <!-- 右侧分段色带 -->
      <div class="color-band-wrapper">
        <div class="color-band">
          <div 
            v-for="(segment, index) in colorSegments" 
            :key="index"
            :class="['band-segment', { active: isActiveSegment(segment) }]"
            :style="{ 
              backgroundColor: segment.color,
              width: segment.width + '%'
            }"
          >
            <!-- 等级标签 -->
            <div 
              v-if="isActiveSegment(segment)" 
              class="level-label"
            >
              {{ segment.level }}
            </div>
          </div>
        </div>
        
        <!-- 数值刻度 -->
        <div class="scale-marks">
          <div 
            v-for="mark in scaleMarks" 
            :key="mark.value"
            class="scale-mark"
            :style="{ left: mark.position + '%' }"
          >
            {{ mark.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  // PM₁₀ 数值
  pmValue: {
    type: Number,
    default: 32
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  },
  // 自定义色带配置
  customSegments: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['dateChange', 'valueUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)

// 默认色带配置（符合空气质量标准）
const defaultColorSegments = [
  { min: 0, max: 50, color: '#00E400', level: '优', width: 20 },
  { min: 51, max: 100, color: '#FFFF00', level: '良', width: 20 },
  { min: 101, max: 150, color: '#FF7E00', level: '轻度污染', width: 20 },
  { min: 151, max: 200, color: '#FF0000', level: '中度污染', width: 20 },
  { min: 201, max: 300, color: '#8F3F97', level: '重度污染', width: 20 }
]

// 色带配置
const colorSegments = computed(() => {
  return props.customSegments.length > 0 ? props.customSegments : defaultColorSegments
})

// 刻度标记
const scaleMarks = computed(() => {
  const marks = []
  let position = 0
  
  colorSegments.value.forEach((segment, index) => {
    marks.push({
      value: segment.min,
      position: position
    })
    position += segment.width
    
    // 添加最后一个刻度
    if (index === colorSegments.value.length - 1) {
      marks.push({
        value: segment.max,
        position: position
      })
    }
  })
  
  return marks
})

// 判断是否为当前活跃的色带段
const isActiveSegment = (segment) => {
  return props.pmValue >= segment.min && props.pmValue <= segment.max
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return '2025.07.01'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}.${month}.${day}`
}

// 处理日期变化
const handleDateChange = () => {
  emit('dateChange', selectedDate.value)
}

// 监听props变化
watch(() => props.pmValue, (newValue) => {
  emit('valueUpdate', newValue)
})

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.color-band-container {
  width: 100%;
  background: rgba(12, 25, 44, 0.9);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

// 顶部区域栏
.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
}

.realtime-label {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.date-picker {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.date-display {
  background: rgba(74, 144, 226, 0.2);
  border: 1px solid rgba(74, 144, 226, 0.5);
  border-radius: 4px;
  padding: 8px 15px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(74, 144, 226, 0.3);
    border-color: #4A90E2;
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  align-items: center;
  gap: 30px;
}

// 左侧PM₁₀数值方块
.pm-value-block {
  background: linear-gradient(135deg, #4A90E2 0%, #1E90FF 100%);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-width: 120px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.pm-value {
  color: #ffffff;
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.pm-label {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

// 右侧色带
.color-band-wrapper {
  flex: 1;
  position: relative;
}

.color-band {
  display: flex;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 15px;
  position: relative;
}

.band-segment {
  position: relative;
  transition: all 0.3s ease;

  &.active {
    box-shadow: inset 0 0 0 2px #ffffff;
    z-index: 2;
  }
}

.level-label {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 3;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
  }
}

// 数值刻度
.scale-marks {
  position: relative;
  height: 20px;
}

.scale-mark {
  position: absolute;
  color: #87CEFA;
  font-size: 12px;
  transform: translateX(-50%);
  
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 1px;
    height: 6px;
    background: #87CEFA;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .pm-value-block {
    align-self: center;
    min-width: 100px;
  }

  .pm-value {
    font-size: 28px;
  }

  .pm-label {
    font-size: 14px;
  }

  .color-band {
    height: 35px;
  }

  .level-label {
    font-size: 11px;
    padding: 3px 6px;
  }

  .header-section {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
